# Ứng dụng Quản lý Tiệm Giặt Ủi

## Mô tả
Ứng dụng Java Swing để quản lý tiệm giặt ủi với các chức năng:
- Qu<PERSON>n lý khách hàng (thê<PERSON>, sử<PERSON>, x<PERSON><PERSON>, tì<PERSON> kiếm)
- Quản lý dịch vụ giặt ủi
- Quản lý đơn hàng
- <PERSON><PERSON> toán hóa đơn
- Tra cứu đơn hàng
- Thống kê doanh thu và đơn hàng

## Yêu cầu hệ thống
- Java JDK 21 hoặc cao hơn
- Maven 3.6 hoặc cao hơn
- SQL Server (có thể dùng SQL Server Express)

## Cài đặt

### 1. Cài đặt Java JDK 21
- Tải và cài đặt từ: https://www.oracle.com/java/technologies/downloads/
- Hoặc sử dụng OpenJDK: https://adoptium.net/

### 2. Cài đặt Maven
- <PERSON><PERSON><PERSON> từ: https://maven.apache.org/download.cgi
- <PERSON><PERSON><PERSON><PERSON> nén và thêm vào PATH

### 3. Cài đặt SQL Server
- Tải SQL Server Express: https://www.microsoft.com/en-us/sql-server/sql-server-downloads
- Hoặc sử dụng SQL Server Developer Edition

### 4. Thiết lập Database
1. Mở SQL Server Management Studio
2. Chạy script `database_script.sql` để tạo database và dữ liệu mẫu
3. Kiểm tra thông tin kết nối trong file `src/main/java/util/DBConnection.java`:
   - URL: `*********************************************************`
   - Username: `sa`
   - Password: `sa`

## Chạy ứng dụng

### Cách 1: Sử dụng Maven
```bash
mvn clean compile exec:java
```

### Cách 2: Compile và chạy trực tiếp
```bash
# Compile
javac -cp "target/classes;lib/*" -d target/classes src/main/java/**/*.java

# Chạy
java -cp "target/classes;lib/*" com.mycompany.giatui.app.GiatUiAPP
```

### Cách 3: Sử dụng IDE
- Mở project trong NetBeans, IntelliJ IDEA hoặc Eclipse
- Chạy class `com.mycompany.giatui.app.GiatUiAPP`

## Cấu trúc dự án
```
src/main/java/
├── com/mycompany/giatui/app/
│   └── GiatUiAPP.java          # Main class
├── model/
│   ├── KhachHang.java          # Model khách hàng
│   ├── KhachHangDAO.java       # DAO khách hàng
│   ├── DichVu.java             # Model dịch vụ
│   ├── DichVuDAO.java          # DAO dịch vụ
│   ├── DonHang.java            # Model đơn hàng
│   ├── DonHangDAO.java         # DAO đơn hàng
│   └── ChiTietDonHang.java     # Model chi tiết đơn hàng
├── util/
│   └── DBConnection.java       # Kết nối database
└── view/
    ├── MainFrame.java          # Giao diện chính
    ├── QuanLyKhachHang.java    # Quản lý khách hàng
    ├── QuanLyDichVu.java       # Quản lý dịch vụ
    ├── QuanLyDonHang.java      # Quản lý đơn hàng
    ├── ThanhToanHoaDon.java    # Thanh toán hóa đơn
    ├── TraCuuDonHang.java      # Tra cứu đơn hàng
    ├── ThongKeDoanhThu.java    # Thống kê doanh thu
    └── ThongKeDonHang.java     # Thống kê đơn hàng
```

## Chức năng đã hoàn thành
- ✅ Kết nối database SQL Server
- ✅ Quản lý khách hàng (CRUD đầy đủ)
- ✅ Quản lý dịch vụ (CRUD đầy đủ)
- ✅ Model và DAO cho đơn hàng
- ✅ Giao diện cơ bản cho tất cả chức năng
- ✅ Menu điều hướng

## Chức năng cần phát triển thêm
- 🔄 Quản lý đơn hàng (tạo đơn, cập nhật trạng thái)
- 🔄 Thanh toán và in hóa đơn
- 🔄 Tra cứu đơn hàng nâng cao
- 🔄 Thống kê chi tiết
- 🔄 Báo cáo

## Lưu ý
- Đảm bảo SQL Server đang chạy trước khi khởi động ứng dụng
- Kiểm tra thông tin kết nối database trong `DBConnection.java`
- Dữ liệu mẫu sẽ được tạo tự động khi chạy script SQL

## Liên hệ
- Tác giả: Minhnghia
- Email: [email của bạn]

## Giấy phép
Dự án này được phát triển cho mục đích học tập.
