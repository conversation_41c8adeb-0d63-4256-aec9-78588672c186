/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package model;

import java.math.BigDecimal;

/**
 * Model class cho Chi tiết đơn hàng
 * <AUTHOR>
 */
public class ChiTietDonHang {
    private int maCTDH;
    private int maDH;
    private int maDV;
    private BigDecimal soLuong;
    private BigDecimal donGia;
    private BigDecimal thanhTien;
    
    // Thông tin dịch vụ (để hiển thị)
    private String tenDichVu;
    private String donVi;
    
    // Constructor mặc định
    public ChiTietDonHang() {
        this.soLuong = BigDecimal.ZERO;
        this.donGia = BigDecimal.ZERO;
        this.thanhTien = BigDecimal.ZERO;
    }
    
    // Constructor có tham số
    public ChiTietDonHang(int maDH, int maDV, BigDecimal soLuong, BigDecimal donGia) {
        this.maDH = maDH;
        this.maDV = maDV;
        this.soLuong = soLuong;
        this.donGia = donGia;
        this.thanhTien = soLuong.multiply(donGia);
    }
    
    // Constructor đầy đủ
    public ChiTietDonHang(int maCTDH, int maDH, int maDV, BigDecimal soLuong, 
                         BigDecimal donGia, BigDecimal thanhTien) {
        this.maCTDH = maCTDH;
        this.maDH = maDH;
        this.maDV = maDV;
        this.soLuong = soLuong;
        this.donGia = donGia;
        this.thanhTien = thanhTien;
    }

    // Getter và Setter methods
    public int getMaCTDH() {
        return maCTDH;
    }

    public void setMaCTDH(int maCTDH) {
        this.maCTDH = maCTDH;
    }

    public int getMaDH() {
        return maDH;
    }

    public void setMaDH(int maDH) {
        this.maDH = maDH;
    }

    public int getMaDV() {
        return maDV;
    }

    public void setMaDV(int maDV) {
        this.maDV = maDV;
    }

    public BigDecimal getSoLuong() {
        return soLuong;
    }

    public void setSoLuong(BigDecimal soLuong) {
        this.soLuong = soLuong;
        tinhLaiThanhTien();
    }

    public BigDecimal getDonGia() {
        return donGia;
    }

    public void setDonGia(BigDecimal donGia) {
        this.donGia = donGia;
        tinhLaiThanhTien();
    }

    public BigDecimal getThanhTien() {
        return thanhTien;
    }

    public void setThanhTien(BigDecimal thanhTien) {
        this.thanhTien = thanhTien;
    }

    public String getTenDichVu() {
        return tenDichVu;
    }

    public void setTenDichVu(String tenDichVu) {
        this.tenDichVu = tenDichVu;
    }

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }
    
    // Phương thức tính lại thành tiền
    public void tinhLaiThanhTien() {
        if (soLuong != null && donGia != null) {
            this.thanhTien = soLuong.multiply(donGia);
        } else {
            this.thanhTien = BigDecimal.ZERO;
        }
    }
    
    // Override toString method
    @Override
    public String toString() {
        return "ChiTietDonHang{" +
                "maCTDH=" + maCTDH +
                ", maDH=" + maDH +
                ", maDV=" + maDV +
                ", soLuong=" + soLuong +
                ", donGia=" + donGia +
                ", thanhTien=" + thanhTien +
                ", tenDichVu='" + tenDichVu + '\'' +
                '}';
    }
    
    // Override equals method
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ChiTietDonHang that = (ChiTietDonHang) obj;
        return maCTDH == that.maCTDH;
    }
    
    // Override hashCode method
    @Override
    public int hashCode() {
        return Integer.hashCode(maCTDH);
    }
}
