@echo off
echo ========================================
echo    Thiet lap Ung dung Quan ly Tiem Giat Ui
echo ========================================
echo.

REM Tao thu muc lib neu chua co
if not exist "lib" mkdir "lib"

REM Tao thu muc target neu chua co
if not exist "target" mkdir "target"
if not exist "target\classes" mkdir "target\classes"
if not exist "target\dependency" mkdir "target\dependency"

echo [INFO] Dang download SQL Server JDBC Driver...

REM Kiem tra ket noi internet truoc
ping google.com -n 1 >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] Khong co ket noi internet. Thu download offline...
    goto :offline_setup
)

REM Download SQL Server JDBC Driver
echo [INFO] Dang download tu Maven Repository...
powershell -Command "try { Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/microsoft/sqlserver/mssql-jdbc/12.6.1.jre11/mssql-jdbc-12.6.1.jre11.jar' -OutFile 'lib\mssql-jdbc-12.6.1.jre11.jar' -TimeoutSec 30 } catch { exit 1 }"

if %errorlevel% neq 0 (
    echo [ERROR] Download that bai. Thu link backup...
    powershell -Command "try { Invoke-WebRequest -Uri 'https://github.com/microsoft/mssql-jdbc/releases/download/v12.6.1/mssql-jdbc-12.6.1.jre11.jar' -OutFile 'lib\mssql-jdbc-12.6.1.jre11.jar' -TimeoutSec 30 } catch { exit 1 }"
)

if exist "lib\mssql-jdbc-12.6.1.jre11.jar" (
    echo [SUCCESS] Da download thanh cong SQL Server JDBC Driver
    
    REM Copy vao target/dependency
    copy "lib\mssql-jdbc-12.6.1.jre11.jar" "target\dependency\"
    
) else (
    echo [ERROR] Khong the download JDBC Driver
    echo Vui long download thu cong tu:
    echo https://repo1.maven.org/maven2/com/microsoft/sqlserver/mssql-jdbc/12.6.1.jre11/mssql-jdbc-12.6.1.jre11.jar
    echo Va dat vao thu muc lib/
)

goto :end

:offline_setup
echo.
echo [INFO] HUONG DAN DOWNLOAD THU CONG:
echo 1. Mo trinh duyet web
echo 2. Vao link: https://repo1.maven.org/maven2/com/microsoft/sqlserver/mssql-jdbc/12.6.1.jre11/mssql-jdbc-12.6.1.jre11.jar
echo 3. Download file ve thu muc lib/
echo 4. Doi ten file thanh: mssql-jdbc-12.6.1.jre11.jar
echo.

:end
echo.
echo [INFO] Thiet lap hoan tat!
echo [INFO] Chay check_environment.bat de kiem tra he thong
echo [INFO] Ban co the chay ung dung bang cach:
echo   1. Double-click vao run.bat
echo   2. Hoac chay lenh: java -cp "target/classes;lib/*" com.mycompany.giatui.app.GiatUiAPP
echo.
pause
