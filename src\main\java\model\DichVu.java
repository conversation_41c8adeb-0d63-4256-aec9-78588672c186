/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package model;

import java.math.BigDecimal;

/**
 * Model class cho Dịch vụ
 * <AUTHOR>
 */
public class DichVu {
    private int maDV;
    private String tenDichVu;
    private BigDecimal giaTien;
    private String donVi;
    private String moTa;
    
    // Constructor mặc định
    public DichVu() {
        this.donVi = "kg";
        this.giaTien = BigDecimal.ZERO;
    }
    
    // Constructor có tham số
    public DichVu(String tenDichVu, BigDecimal giaTien, String donVi, String moTa) {
        this.tenDichVu = tenDichVu;
        this.giaTien = giaTien;
        this.donVi = donVi;
        this.moTa = moTa;
    }
    
    // Constructor đầy đủ
    public DichVu(int maDV, String tenDichVu, BigDecimal giaTien, String donVi, String moTa) {
        this.maDV = maDV;
        this.tenDichVu = tenDichVu;
        this.giaTien = giaTien;
        this.donVi = donVi;
        this.moTa = moTa;
    }

    // Getter và Setter methods
    public int getMaDV() {
        return maDV;
    }

    public void setMaDV(int maDV) {
        this.maDV = maDV;
    }

    public String getTenDichVu() {
        return tenDichVu;
    }

    public void setTenDichVu(String tenDichVu) {
        this.tenDichVu = tenDichVu;
    }

    public BigDecimal getGiaTien() {
        return giaTien;
    }

    public void setGiaTien(BigDecimal giaTien) {
        this.giaTien = giaTien;
    }

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }

    public String getMoTa() {
        return moTa;
    }

    public void setMoTa(String moTa) {
        this.moTa = moTa;
    }
    
    // Phương thức tính thành tiền
    public BigDecimal tinhThanhTien(BigDecimal soLuong) {
        if (soLuong == null || giaTien == null) {
            return BigDecimal.ZERO;
        }
        return giaTien.multiply(soLuong);
    }
    
    // Override toString method
    @Override
    public String toString() {
        return "DichVu{" +
                "maDV=" + maDV +
                ", tenDichVu='" + tenDichVu + '\'' +
                ", giaTien=" + giaTien +
                ", donVi='" + donVi + '\'' +
                ", moTa='" + moTa + '\'' +
                '}';
    }
    
    // Override equals method
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        DichVu dichVu = (DichVu) obj;
        return maDV == dichVu.maDV;
    }
    
    // Override hashCode method
    @Override
    public int hashCode() {
        return Integer.hashCode(maDV);
    }
    
    // Phương thức để hiển thị trong ComboBox
    public String getDisplayText() {
        return tenDichVu + " - " + giaTien + "đ/" + donVi;
    }
}
