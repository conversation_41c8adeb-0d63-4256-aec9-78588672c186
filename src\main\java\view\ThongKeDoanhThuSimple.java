package view;

import model.*;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.math.BigDecimal;
import java.util.List;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * Thống kê doanh thu - <PERSON>ên bản đơn giản
 * <AUTHOR>
 */
public class ThongKeDoanhThuSimple extends JPanel {
    
    private DonHangDAO donHangDAO;
    private DefaultTableModel tableModel;
    
    // Components
    private JTextField txtTuNgay;
    private JTextField txtDenNgay;
    private JButton btnThongKe;
    private JLabel lblTongDoanhThu;
    private JTable tblThongKe;

    public ThongKeDoanhThuSimple() {
        try {
            donHangDAO = new DonHangDAO();
            initComponents();
            setupTable();
            setupEventHandlers();
            loadData();
        } catch (Exception e) {
            System.err.println("❌ Lỗi khởi tạo ThongKeDoanhThu: " + e.getMessage());
            e.printStackTrace();
            JOptionPane.showMessageDialog(this, 
                "Lỗi khởi tạo thống kê doanh thu!", 
                "Lỗi", 
                JOptionPane.ERROR_MESSAGE);
        }
    }
    
    private void initComponents() {
        setLayout(new BorderLayout());
        
        // Panel tiêu đề
        JPanel titlePanel = new JPanel();
        JLabel titleLabel = new JLabel("THỐNG KÊ DOANH THU");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 18));
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        titlePanel.add(titleLabel);
        add(titlePanel, BorderLayout.NORTH);
        
        // Panel filter
        JPanel filterPanel = new JPanel(new FlowLayout());
        filterPanel.setBorder(BorderFactory.createTitledBorder("Bộ lọc"));
        
        filterPanel.add(new JLabel("Từ ngày:"));
        txtTuNgay = new JTextField(10);
        txtTuNgay.setText(LocalDate.now().minusMonths(1).format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
        filterPanel.add(txtTuNgay);
        
        filterPanel.add(new JLabel("Đến ngày:"));
        txtDenNgay = new JTextField(10);
        txtDenNgay.setText(LocalDate.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
        filterPanel.add(txtDenNgay);
        
        btnThongKe = new JButton("Thống kê");
        filterPanel.add(btnThongKe);
        
        add(filterPanel, BorderLayout.NORTH);
        
        // Panel kết quả
        JPanel resultPanel = new JPanel(new BorderLayout());
        
        // Tổng doanh thu
        JPanel summaryPanel = new JPanel(new FlowLayout());
        summaryPanel.setBorder(BorderFactory.createTitledBorder("Tổng kết"));
        lblTongDoanhThu = new JLabel("Tổng doanh thu: 0 VNĐ");
        lblTongDoanhThu.setFont(new Font("Arial", Font.BOLD, 16));
        lblTongDoanhThu.setForeground(Color.BLUE);
        summaryPanel.add(lblTongDoanhThu);
        resultPanel.add(summaryPanel, BorderLayout.NORTH);
        
        // Table
        String[] columnNames = {"Ngày", "Số đơn hàng", "Doanh thu"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        tblThongKe = new JTable(tableModel);
        JScrollPane scrollPane = new JScrollPane(tblThongKe);
        scrollPane.setBorder(BorderFactory.createTitledBorder("Chi tiết doanh thu"));
        resultPanel.add(scrollPane, BorderLayout.CENTER);
        
        add(resultPanel, BorderLayout.CENTER);
    }
    
    private void setupTable() {
        tblThongKe.getColumnModel().getColumn(0).setPreferredWidth(100);
        tblThongKe.getColumnModel().getColumn(1).setPreferredWidth(100);
        tblThongKe.getColumnModel().getColumn(2).setPreferredWidth(150);
    }
    
    private void setupEventHandlers() {
        btnThongKe.addActionListener(e -> thongKeDoanhThu());
    }
    
    private void loadData() {
        // Load dữ liệu mặc định (30 ngày gần nhất)
        thongKeDoanhThu();
    }
    
    private void thongKeDoanhThu() {
        try {
            // Xóa dữ liệu cũ
            tableModel.setRowCount(0);
            
            // Lấy tất cả đơn hàng (tạm thời)
            List<DonHang> danhSach = donHangDAO.layTatCaDonHang();
            
            BigDecimal tongDoanhThu = BigDecimal.ZERO;
            int tongDonHang = 0;
            
            // Tính toán doanh thu theo ngày (đơn giản)
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            
            for (DonHang dh : danhSach) {
                if ("Hoàn thành".equals(dh.getTrangThai())) {
                    String ngay = dh.getNgayTao().format(formatter);
                    BigDecimal doanhThu = dh.getTongTien();
                    
                    // Thêm vào bảng
                    Object[] row = {
                        ngay,
                        1,
                        String.format("%,.0f VNĐ", doanhThu.doubleValue())
                    };
                    tableModel.addRow(row);
                    
                    tongDoanhThu = tongDoanhThu.add(doanhThu);
                    tongDonHang++;
                }
            }
            
            // Cập nhật tổng kết
            lblTongDoanhThu.setText(String.format("Tổng doanh thu: %,.0f VNĐ (%d đơn hàng)", 
                tongDoanhThu.doubleValue(), tongDonHang));
            
        } catch (Exception e) {
            System.err.println("❌ Lỗi thống kê doanh thu: " + e.getMessage());
            e.printStackTrace();
            JOptionPane.showMessageDialog(this, 
                "Lỗi thống kê doanh thu: " + e.getMessage(), 
                "Lỗi", 
                JOptionPane.ERROR_MESSAGE);
        }
    }
}
