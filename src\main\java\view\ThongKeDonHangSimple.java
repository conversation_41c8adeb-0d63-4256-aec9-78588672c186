package view;

import model.*;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * Thống kê đơn hàng - <PERSON>ên bản đơn giản
 * <AUTHOR>
 */
public class ThongKeDonHangSimple extends JPanel {
    
    private DonHangDAO donHangDAO;
    private DefaultTableModel tableModel;
    
    // Components
    private JButton btnThongKe;
    private JLabel lblTongDonHang;
    private JTable tblThongKe;

    public ThongKeDonHangSimple() {
        try {
            donHangDAO = new DonHangDAO();
            initComponents();
            setupTable();
            setupEventHandlers();
            loadData();
        } catch (Exception e) {
            System.err.println("❌ Lỗi khởi tạo ThongKeDonHang: " + e.getMessage());
            e.printStackTrace();
            JOptionPane.showMessageDialog(this, 
                "Lỗi khởi tạo thống kê đơn hàng!", 
                "Lỗi", 
                JOptionPane.ERROR_MESSAGE);
        }
    }
    
    private void initComponents() {
        setLayout(new BorderLayout());
        
        // Panel tiêu đề
        JPanel titlePanel = new JPanel();
        JLabel titleLabel = new JLabel("THỐNG KÊ ĐƠN HÀNG");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 18));
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        titlePanel.add(titleLabel);
        add(titlePanel, BorderLayout.NORTH);
        
        // Panel control
        JPanel controlPanel = new JPanel(new FlowLayout());
        btnThongKe = new JButton("Làm mới thống kê");
        controlPanel.add(btnThongKe);
        
        lblTongDonHang = new JLabel("Tổng số đơn hàng: 0");
        lblTongDonHang.setFont(new Font("Arial", Font.BOLD, 14));
        controlPanel.add(lblTongDonHang);
        
        add(controlPanel, BorderLayout.NORTH);
        
        // Table
        String[] columnNames = {"Trạng thái", "Số lượng", "Tỷ lệ (%)"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        tblThongKe = new JTable(tableModel);
        JScrollPane scrollPane = new JScrollPane(tblThongKe);
        scrollPane.setBorder(BorderFactory.createTitledBorder("Thống kê theo trạng thái"));
        add(scrollPane, BorderLayout.CENTER);
    }
    
    private void setupTable() {
        tblThongKe.getColumnModel().getColumn(0).setPreferredWidth(150);
        tblThongKe.getColumnModel().getColumn(1).setPreferredWidth(100);
        tblThongKe.getColumnModel().getColumn(2).setPreferredWidth(100);
    }
    
    private void setupEventHandlers() {
        btnThongKe.addActionListener(e -> loadData());
    }
    
    private void loadData() {
        try {
            // Xóa dữ liệu cũ
            tableModel.setRowCount(0);
            
            // Lấy tất cả đơn hàng
            List<DonHang> danhSach = donHangDAO.layTatCaDonHang();
            
            // Đếm theo trạng thái
            Map<String, Integer> thongKe = new HashMap<>();
            for (DonHang dh : danhSach) {
                String trangThai = dh.getTrangThai();
                thongKe.put(trangThai, thongKe.getOrDefault(trangThai, 0) + 1);
            }
            
            int tongDonHang = danhSach.size();
            
            // Thêm vào bảng
            for (Map.Entry<String, Integer> entry : thongKe.entrySet()) {
                String trangThai = entry.getKey();
                int soLuong = entry.getValue();
                double tyLe = tongDonHang > 0 ? (double) soLuong / tongDonHang * 100 : 0;
                
                Object[] row = {
                    trangThai,
                    soLuong,
                    String.format("%.1f%%", tyLe)
                };
                tableModel.addRow(row);
            }
            
            // Cập nhật tổng số
            lblTongDonHang.setText("Tổng số đơn hàng: " + tongDonHang);
            
        } catch (Exception e) {
            System.err.println("❌ Lỗi thống kê đơn hàng: " + e.getMessage());
            e.printStackTrace();
            JOptionPane.showMessageDialog(this, 
                "Lỗi thống kê đơn hàng: " + e.getMessage(), 
                "Lỗi", 
                JOptionPane.ERROR_MESSAGE);
        }
    }
}
