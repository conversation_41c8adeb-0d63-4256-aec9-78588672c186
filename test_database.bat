@echo off
echo ========================================
echo    TEST KET NOI DATABASE
echo ========================================
echo.

REM Kiem tra Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java chua duoc cai dat
    pause
    exit /b 1
)

REM Kiem tra JDBC Driver
if not exist "lib\mssql-jdbc-12.6.1.jre11.jar" (
    if not exist "target\dependency\mssql-jdbc-12.6.1.jre11.jar" (
        echo ❌ JDBC Driver chua duoc download
        echo Chay setup.bat truoc
        pause
        exit /b 1
    )
)

REM Tao thu muc target/classes neu chua co
if not exist "target\classes" mkdir "target\classes"

echo [INFO] Dang compile DBConnection...
javac -cp "lib/*;target/dependency/*" -d "target/classes" src/main/java/util/DBConnection.java

if %errorlevel% neq 0 (
    echo ❌ Compile that bai
    pause
    exit /b 1
)

echo [INFO] Dang test ket noi database...
echo.
java -cp "target/classes;lib/*;target/dependency/*" util.DBConnection

echo.
echo [INFO] Test hoan tat!
pause
