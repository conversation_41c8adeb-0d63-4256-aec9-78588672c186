package view;

import model.KhachHang;
import model.KhachHangDAO;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;
import java.time.format.DateTimeFormatter;

/**
 * Quản lý khách hàng - Phiên bản đơn giản không dùng Form Editor
 * <AUTHOR>
 */
public class QuanLyKhachHangSimple extends JPanel {
    
    private KhachHangDAO khachHangDAO;
    private DefaultTableModel tableModel;
    private int selectedRow = -1;
    
    // Components
    private JTextField txthoten;
    private JTextField txtsdt;
    private JTextField txtdiachi;
    private JButton btnthem;
    private JButton btnsua;
    private JButton btnxoa;
    private JTable tblchitietkhachhang;

    public QuanLyKhachHangSimple() {
        try {
            khachHangDAO = new KhachHangDAO();
            initComponents();
            setupTable();
            setupEventHandlers();
            loadData();
        } catch (Exception e) {
            System.err.println("❌ Lỗi khởi tạo QuanLyKhachHang: " + e.getMessage());
            e.printStackTrace();
            JOptionPane.showMessageDialog(this, 
                "Lỗi khởi tạo quản lý khách hàng. Kiểm tra kết nối database!", 
                "Lỗi", 
                JOptionPane.ERROR_MESSAGE);
        }
    }
    
    private void initComponents() {
        setLayout(new BorderLayout());
        
        // Panel tiêu đề
        JPanel titlePanel = new JPanel();
        JLabel titleLabel = new JLabel("QUẢN LÝ KHÁCH HÀNG");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 18));
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        titlePanel.add(titleLabel);
        add(titlePanel, BorderLayout.NORTH);
        
        // Panel form nhập liệu
        JPanel formPanel = new JPanel(new GridBagLayout());
        formPanel.setBorder(BorderFactory.createTitledBorder("Thông tin khách hàng"));
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        // Họ tên
        gbc.gridx = 0; gbc.gridy = 0;
        formPanel.add(new JLabel("Họ tên:"), gbc);
        gbc.gridx = 1;
        txthoten = new JTextField(20);
        formPanel.add(txthoten, gbc);
        
        // Số điện thoại
        gbc.gridx = 0; gbc.gridy = 1;
        formPanel.add(new JLabel("Số điện thoại:"), gbc);
        gbc.gridx = 1;
        txtsdt = new JTextField(20);
        formPanel.add(txtsdt, gbc);
        
        // Địa chỉ
        gbc.gridx = 0; gbc.gridy = 2;
        formPanel.add(new JLabel("Địa chỉ:"), gbc);
        gbc.gridx = 1;
        txtdiachi = new JTextField(20);
        formPanel.add(txtdiachi, gbc);
        
        // Buttons
        JPanel buttonPanel = new JPanel(new FlowLayout());
        btnthem = new JButton("Thêm");
        btnsua = new JButton("Sửa");
        btnxoa = new JButton("Xóa");
        
        buttonPanel.add(btnthem);
        buttonPanel.add(btnsua);
        buttonPanel.add(btnxoa);
        
        gbc.gridx = 0; gbc.gridy = 3;
        gbc.gridwidth = 2;
        formPanel.add(buttonPanel, gbc);
        
        add(formPanel, BorderLayout.WEST);
        
        // Table
        String[] columnNames = {"Mã KH", "Họ tên", "Số điện thoại", "Địa chỉ", "Ngày tạo"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        tblchitietkhachhang = new JTable(tableModel);
        JScrollPane scrollPane = new JScrollPane(tblchitietkhachhang);
        scrollPane.setBorder(BorderFactory.createTitledBorder("Danh sách khách hàng"));
        add(scrollPane, BorderLayout.CENTER);
    }
    
    private void setupTable() {
        // Thiết lập độ rộng cột
        tblchitietkhachhang.getColumnModel().getColumn(0).setPreferredWidth(60);
        tblchitietkhachhang.getColumnModel().getColumn(1).setPreferredWidth(150);
        tblchitietkhachhang.getColumnModel().getColumn(2).setPreferredWidth(120);
        tblchitietkhachhang.getColumnModel().getColumn(3).setPreferredWidth(200);
        tblchitietkhachhang.getColumnModel().getColumn(4).setPreferredWidth(120);
        
        // Xử lý sự kiện chọn dòng
        tblchitietkhachhang.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                selectedRow = tblchitietkhachhang.getSelectedRow();
                if (selectedRow >= 0) {
                    loadSelectedRowToForm();
                }
            }
        });
    }
    
    private void setupEventHandlers() {
        btnthem.addActionListener(e -> themKhachHang());
        btnsua.addActionListener(e -> suaKhachHang());
        btnxoa.addActionListener(e -> xoaKhachHang());
    }
    
    private void loadData() {
        tableModel.setRowCount(0);
        List<KhachHang> danhSach = khachHangDAO.layTatCaKhachHang();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
        
        for (KhachHang kh : danhSach) {
            Object[] row = {
                kh.getMaKH(),
                kh.getHoTen(),
                kh.getSoDienThoai(),
                kh.getDiaChi(),
                kh.getNgayTao().format(formatter)
            };
            tableModel.addRow(row);
        }
    }
    
    private void loadSelectedRowToForm() {
        if (selectedRow >= 0) {
            txthoten.setText(tableModel.getValueAt(selectedRow, 1).toString());
            txtsdt.setText(tableModel.getValueAt(selectedRow, 2).toString());
            txtdiachi.setText(tableModel.getValueAt(selectedRow, 3).toString());
        }
    }
    
    private void clearForm() {
        txthoten.setText("");
        txtsdt.setText("");
        txtdiachi.setText("");
        selectedRow = -1;
        tblchitietkhachhang.clearSelection();
    }
    
    private void themKhachHang() {
        if (!validateForm()) {
            return;
        }
        
        String hoTen = txthoten.getText().trim();
        String sdt = txtsdt.getText().trim();
        String diaChi = txtdiachi.getText().trim();
        
        if (khachHangDAO.kiemTraSDTTonTai(sdt, 0)) {
            JOptionPane.showMessageDialog(this, "Số điện thoại đã tồn tại!", "Lỗi", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        KhachHang kh = new KhachHang(hoTen, sdt, diaChi);
        
        if (khachHangDAO.themKhachHang(kh)) {
            JOptionPane.showMessageDialog(this, "Thêm khách hàng thành công!", "Thông báo", JOptionPane.INFORMATION_MESSAGE);
            loadData();
            clearForm();
        } else {
            JOptionPane.showMessageDialog(this, "Thêm khách hàng thất bại!", "Lỗi", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    private void suaKhachHang() {
        if (selectedRow < 0) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn khách hàng cần sửa!", "Thông báo", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        if (!validateForm()) {
            return;
        }
        
        int maKH = (Integer) tableModel.getValueAt(selectedRow, 0);
        String hoTen = txthoten.getText().trim();
        String sdt = txtsdt.getText().trim();
        String diaChi = txtdiachi.getText().trim();
        
        if (khachHangDAO.kiemTraSDTTonTai(sdt, maKH)) {
            JOptionPane.showMessageDialog(this, "Số điện thoại đã tồn tại!", "Lỗi", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        KhachHang kh = new KhachHang();
        kh.setMaKH(maKH);
        kh.setHoTen(hoTen);
        kh.setSoDienThoai(sdt);
        kh.setDiaChi(diaChi);
        
        if (khachHangDAO.suaKhachHang(kh)) {
            JOptionPane.showMessageDialog(this, "Sửa thông tin khách hàng thành công!", "Thông báo", JOptionPane.INFORMATION_MESSAGE);
            loadData();
            clearForm();
        } else {
            JOptionPane.showMessageDialog(this, "Sửa thông tin khách hàng thất bại!", "Lỗi", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    private void xoaKhachHang() {
        if (selectedRow < 0) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn khách hàng cần xóa!", "Thông báo", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        int maKH = (Integer) tableModel.getValueAt(selectedRow, 0);
        String hoTen = tableModel.getValueAt(selectedRow, 1).toString();
        
        int confirm = JOptionPane.showConfirmDialog(this, 
            "Bạn có chắc chắn muốn xóa khách hàng: " + hoTen + "?", 
            "Xác nhận xóa", 
            JOptionPane.YES_NO_OPTION);
        
        if (confirm == JOptionPane.YES_OPTION) {
            if (khachHangDAO.xoaKhachHang(maKH)) {
                JOptionPane.showMessageDialog(this, "Xóa khách hàng thành công!", "Thông báo", JOptionPane.INFORMATION_MESSAGE);
                loadData();
                clearForm();
            } else {
                JOptionPane.showMessageDialog(this, "Xóa khách hàng thất bại! Có thể khách hàng đã có đơn hàng.", "Lỗi", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    private boolean validateForm() {
        String hoTen = txthoten.getText().trim();
        String sdt = txtsdt.getText().trim();
        String diaChi = txtdiachi.getText().trim();
        
        if (hoTen.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Vui lòng nhập họ tên!", "Lỗi", JOptionPane.ERROR_MESSAGE);
            txthoten.requestFocus();
            return false;
        }
        
        if (sdt.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Vui lòng nhập số điện thoại!", "Lỗi", JOptionPane.ERROR_MESSAGE);
            txtsdt.requestFocus();
            return false;
        }
        
        if (!sdt.matches("^[0-9]{10,11}$")) {
            JOptionPane.showMessageDialog(this, "Số điện thoại không hợp lệ! (10-11 chữ số)", "Lỗi", JOptionPane.ERROR_MESSAGE);
            txtsdt.requestFocus();
            return false;
        }
        
        if (diaChi.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Vui lòng nhập địa chỉ!", "Lỗi", JOptionPane.ERROR_MESSAGE);
            txtdiachi.requestFocus();
            return false;
        }
        
        return true;
    }
}
