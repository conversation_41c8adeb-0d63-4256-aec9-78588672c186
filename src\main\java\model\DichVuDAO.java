/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package model;

import util.DBConnection;
import java.sql.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Data Access Object cho Dịch vụ
 * <AUTHOR>
 */
public class DichVuDAO {
    
    // Thêm dịch vụ mới
    public boolean themDichVu(DichVu dv) {
        String sql = "INSERT INTO DichVu (TenDichVu, GiaTien, DonVi, MoTa) VALUES (?, ?, ?, ?)";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, dv.getTenDichVu());
            pstmt.setBigDecimal(2, dv.getGiaTien());
            pstmt.setString(3, dv.getDonVi());
            pstmt.setString(4, dv.getMoTa());
            
            int result = pstmt.executeUpdate();
            return result > 0;
            
        } catch (SQLException e) {
            System.out.println("Lỗi khi thêm dịch vụ: " + e.getMessage());
            return false;
        }
    }
    
    // Sửa thông tin dịch vụ
    public boolean suaDichVu(DichVu dv) {
        String sql = "UPDATE DichVu SET TenDichVu = ?, GiaTien = ?, DonVi = ?, MoTa = ? WHERE MaDV = ?";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, dv.getTenDichVu());
            pstmt.setBigDecimal(2, dv.getGiaTien());
            pstmt.setString(3, dv.getDonVi());
            pstmt.setString(4, dv.getMoTa());
            pstmt.setInt(5, dv.getMaDV());
            
            int result = pstmt.executeUpdate();
            return result > 0;
            
        } catch (SQLException e) {
            System.out.println("Lỗi khi sửa dịch vụ: " + e.getMessage());
            return false;
        }
    }
    
    // Xóa dịch vụ
    public boolean xoaDichVu(int maDV) {
        String sql = "DELETE FROM DichVu WHERE MaDV = ?";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, maDV);
            
            int result = pstmt.executeUpdate();
            return result > 0;
            
        } catch (SQLException e) {
            System.out.println("Lỗi khi xóa dịch vụ: " + e.getMessage());
            return false;
        }
    }
    
    // Tìm dịch vụ theo mã
    public DichVu timDichVuTheoMa(int maDV) {
        String sql = "SELECT * FROM DichVu WHERE MaDV = ?";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, maDV);
            ResultSet rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return taoDichVuTuResultSet(rs);
            }
            
        } catch (SQLException e) {
            System.out.println("Lỗi khi tìm dịch vụ: " + e.getMessage());
        }
        return null;
    }
    
    // Tìm kiếm dịch vụ theo tên
    public List<DichVu> timDichVuTheoTen(String ten) {
        List<DichVu> danhSach = new ArrayList<>();
        String sql = "SELECT * FROM DichVu WHERE TenDichVu LIKE ? ORDER BY TenDichVu";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, "%" + ten + "%");
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                danhSach.add(taoDichVuTuResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.out.println("Lỗi khi tìm dịch vụ theo tên: " + e.getMessage());
        }
        return danhSach;
    }
    
    // Lấy tất cả dịch vụ
    public List<DichVu> layTatCaDichVu() {
        List<DichVu> danhSach = new ArrayList<>();
        String sql = "SELECT * FROM DichVu ORDER BY TenDichVu";
        try (Connection conn = DBConnection.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                danhSach.add(taoDichVuTuResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.out.println("Lỗi khi lấy danh sách dịch vụ: " + e.getMessage());
        }
        return danhSach;
    }
    
    // Đếm tổng số dịch vụ
    public int demTongSoDichVu() {
        String sql = "SELECT COUNT(*) FROM DichVu";
        try (Connection conn = DBConnection.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            if (rs.next()) {
                return rs.getInt(1);
            }
            
        } catch (SQLException e) {
            System.out.println("Lỗi khi đếm dịch vụ: " + e.getMessage());
        }
        return 0;
    }
    
    // Kiểm tra tên dịch vụ đã tồn tại chưa
    public boolean kiemTraTenDichVuTonTai(String tenDichVu, int maDVLoaiTru) {
        String sql = "SELECT COUNT(*) FROM DichVu WHERE TenDichVu = ? AND MaDV != ?";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, tenDichVu);
            pstmt.setInt(2, maDVLoaiTru);
            ResultSet rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
            
        } catch (SQLException e) {
            System.out.println("Lỗi khi kiểm tra tên dịch vụ: " + e.getMessage());
        }
        return false;
    }
    
    // Lấy dịch vụ theo khoảng giá
    public List<DichVu> layDichVuTheoKhoangGia(BigDecimal giaMin, BigDecimal giaMax) {
        List<DichVu> danhSach = new ArrayList<>();
        String sql = "SELECT * FROM DichVu WHERE GiaTien BETWEEN ? AND ? ORDER BY GiaTien";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setBigDecimal(1, giaMin);
            pstmt.setBigDecimal(2, giaMax);
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                danhSach.add(taoDichVuTuResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.out.println("Lỗi khi lấy dịch vụ theo khoảng giá: " + e.getMessage());
        }
        return danhSach;
    }
    
    // Phương thức helper để tạo đối tượng DichVu từ ResultSet
    private DichVu taoDichVuTuResultSet(ResultSet rs) throws SQLException {
        int maDV = rs.getInt("MaDV");
        String tenDichVu = rs.getString("TenDichVu");
        BigDecimal giaTien = rs.getBigDecimal("GiaTien");
        String donVi = rs.getString("DonVi");
        String moTa = rs.getString("MoTa");
        
        return new DichVu(maDV, tenDichVu, giaTien, donVi, moTa);
    }
}
