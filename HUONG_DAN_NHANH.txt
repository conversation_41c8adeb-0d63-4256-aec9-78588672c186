========================================
    HƯỚNG DẪN NHANH - ỨNG DỤNG QUẢN LÝ TIỆM GIẶT ỦI
========================================

BƯỚC 1: CÀI ĐẶT JAVA
- Tải Java JDK 21: https://www.oracle.com/java/technologies/downloads/
- Cài đặt và thêm vào PATH

BƯỚC 2: CÀI ĐẶT SQL SERVER
- Tải SQL Server Express: https://www.microsoft.com/en-us/sql-server/sql-server-downloads
- Cài đặt với username: sa, password: sa

BƯỚC 3: TẠO DATABASE
- Mở SQL Server Management Studio
- Chạy file database_script.sql để tạo database và dữ liệu mẫu

BƯỚC 4: THIẾT LẬP ỨNG DỤNG
- Double-click vào setup.bat để download dependencies
- Hoặc download thủ công JDBC driver vào thư mục lib/

BƯỚC 5: CHẠY ỨNG DỤNG
- Double-click vào run.bat
- Hoặc chạy lệnh: java -cp "target/classes;lib/*" com.mycompany.giatui.app.GiatUiAPP

========================================
CHỨC NĂNG ĐÃ HOÀN THÀNH:
========================================

✅ QUẢN LÝ KHÁCH HÀNG:
   - Thêm khách hàng mới
   - Sửa thông tin khách hàng
   - Xóa khách hàng
   - Tìm kiếm khách hàng
   - Hiển thị danh sách khách hàng

✅ QUẢN LÝ DỊCH VỤ:
   - Thêm dịch vụ mới
   - Sửa thông tin dịch vụ
   - Xóa dịch vụ
   - Hiển thị danh sách dịch vụ

✅ GIAO DIỆN CƠ BẢN:
   - Menu điều hướng
   - Các form quản lý
   - Kết nối database

========================================
CHỨC NĂNG ĐANG PHÁT TRIỂN:
========================================

🔄 Quản lý đơn hàng chi tiết
🔄 Thanh toán và in hóa đơn
🔄 Tra cứu đơn hàng nâng cao
🔄 Thống kê chi tiết

========================================
LƯU Ý QUAN TRỌNG:
========================================

1. Đảm bảo SQL Server đang chạy
2. Kiểm tra thông tin kết nối trong DBConnection.java
3. Username/Password mặc định: sa/sa
4. Database name: QuanLyGiatUi

========================================
LIÊN HỆ HỖ TRỢ:
========================================

Nếu gặp lỗi, vui lòng kiểm tra:
1. Java đã được cài đặt và thêm vào PATH
2. SQL Server đang chạy
3. Database đã được tạo
4. JDBC driver đã được download

Tác giả: Minhnghia
========================================
