package view;

import model.*;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.math.BigDecimal;
import java.util.List;
import java.time.format.DateTimeFormatter;

/**
 * Quản lý đơn hàng - Phiên bản đơn giản không dùng Form Editor
 * <AUTHOR>
 */
public class QuanLyDonHangSimple extends JPanel {
    
    private DonHangDAO donHangDAO;
    private KhachHangDAO khachHangDAO;
    private DichVuDAO dichVuDAO;
    private DefaultTableModel tableModel;
    private int selectedRow = -1;
    
    // Components
    private JComboBox<String> cboKhachHang;
    private JComboBox<String> cboDichVu;
    private JTextField txtSoLuong;
    private JTextField txtGhiChu;
    private JButton btnThem;
    private JButton btnSua;
    private JButton btnXoa;
    private JTable tblDonHang;

    public QuanLyDonHangSimple() {
        try {
            donHangDAO = new DonHangDAO();
            khachHangDAO = new KhachHangDAO();
            dichVuDAO = new DichVuDAO();
            initComponents();
            setupTable();
            setupEventHandlers();
            loadComboBoxData();
            loadData();
        } catch (Exception e) {
            System.err.println("❌ Lỗi khởi tạo QuanLyDonHang: " + e.getMessage());
            e.printStackTrace();
            JOptionPane.showMessageDialog(this, 
                "Lỗi khởi tạo quản lý đơn hàng. Kiểm tra kết nối database!", 
                "Lỗi", 
                JOptionPane.ERROR_MESSAGE);
        }
    }
    
    private void initComponents() {
        setLayout(new BorderLayout());
        
        // Panel tiêu đề
        JPanel titlePanel = new JPanel();
        JLabel titleLabel = new JLabel("QUẢN LÝ ĐỚN HÀNG");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 18));
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        titlePanel.add(titleLabel);
        add(titlePanel, BorderLayout.NORTH);
        
        // Panel form nhập liệu
        JPanel formPanel = new JPanel(new GridBagLayout());
        formPanel.setBorder(BorderFactory.createTitledBorder("Thông tin đơn hàng"));
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        // Khách hàng
        gbc.gridx = 0; gbc.gridy = 0;
        formPanel.add(new JLabel("Khách hàng:"), gbc);
        gbc.gridx = 1;
        cboKhachHang = new JComboBox<>();
        cboKhachHang.setPreferredSize(new Dimension(200, 25));
        formPanel.add(cboKhachHang, gbc);
        
        // Dịch vụ
        gbc.gridx = 0; gbc.gridy = 1;
        formPanel.add(new JLabel("Dịch vụ:"), gbc);
        gbc.gridx = 1;
        cboDichVu = new JComboBox<>();
        cboDichVu.setPreferredSize(new Dimension(200, 25));
        formPanel.add(cboDichVu, gbc);
        
        // Số lượng
        gbc.gridx = 0; gbc.gridy = 2;
        formPanel.add(new JLabel("Số lượng:"), gbc);
        gbc.gridx = 1;
        txtSoLuong = new JTextField(20);
        formPanel.add(txtSoLuong, gbc);
        
        // Ghi chú
        gbc.gridx = 0; gbc.gridy = 3;
        formPanel.add(new JLabel("Ghi chú:"), gbc);
        gbc.gridx = 1;
        txtGhiChu = new JTextField(20);
        formPanel.add(txtGhiChu, gbc);
        
        // Buttons
        JPanel buttonPanel = new JPanel(new FlowLayout());
        btnThem = new JButton("Thêm");
        btnSua = new JButton("Sửa");
        btnXoa = new JButton("Xóa");
        
        buttonPanel.add(btnThem);
        buttonPanel.add(btnSua);
        buttonPanel.add(btnXoa);
        
        gbc.gridx = 0; gbc.gridy = 4;
        gbc.gridwidth = 2;
        formPanel.add(buttonPanel, gbc);
        
        add(formPanel, BorderLayout.WEST);
        
        // Table
        String[] columnNames = {"Mã ĐH", "Khách hàng", "Tổng tiền", "Trạng thái", "Ngày tạo", "Ghi chú"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        tblDonHang = new JTable(tableModel);
        JScrollPane scrollPane = new JScrollPane(tblDonHang);
        scrollPane.setBorder(BorderFactory.createTitledBorder("Danh sách đơn hàng"));
        add(scrollPane, BorderLayout.CENTER);
    }
    
    private void setupTable() {
        // Thiết lập độ rộng cột
        tblDonHang.getColumnModel().getColumn(0).setPreferredWidth(60);
        tblDonHang.getColumnModel().getColumn(1).setPreferredWidth(150);
        tblDonHang.getColumnModel().getColumn(2).setPreferredWidth(100);
        tblDonHang.getColumnModel().getColumn(3).setPreferredWidth(100);
        tblDonHang.getColumnModel().getColumn(4).setPreferredWidth(120);
        tblDonHang.getColumnModel().getColumn(5).setPreferredWidth(200);
        
        // Xử lý sự kiện chọn dòng
        tblDonHang.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                selectedRow = tblDonHang.getSelectedRow();
                if (selectedRow >= 0) {
                    loadSelectedRowToForm();
                }
            }
        });
    }
    
    private void setupEventHandlers() {
        btnThem.addActionListener(e -> themDonHang());
        btnSua.addActionListener(e -> suaDonHang());
        btnXoa.addActionListener(e -> xoaDonHang());
    }
    
    private void loadComboBoxData() {
        // Load khách hàng
        cboKhachHang.removeAllItems();
        cboKhachHang.addItem("-- Chọn khách hàng --");
        List<KhachHang> khachHangs = khachHangDAO.layTatCaKhachHang();
        for (KhachHang kh : khachHangs) {
            cboKhachHang.addItem(kh.getMaKH() + " - " + kh.getHoTen());
        }
        
        // Load dịch vụ
        cboDichVu.removeAllItems();
        cboDichVu.addItem("-- Chọn dịch vụ --");
        List<DichVu> dichVus = dichVuDAO.layTatCaDichVu();
        for (DichVu dv : dichVus) {
            cboDichVu.addItem(dv.getMaDV() + " - " + dv.getTenDV());
        }
    }
    
    private void loadData() {
        tableModel.setRowCount(0);
        List<DonHang> danhSach = donHangDAO.layTatCaDonHang();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
        
        for (DonHang dh : danhSach) {
            KhachHang kh = khachHangDAO.layKhachHangTheoMa(dh.getMaKH());
            Object[] row = {
                dh.getMaDH(),
                kh != null ? kh.getHoTen() : "N/A",
                String.format("%,.0f VNĐ", dh.getTongTien().doubleValue()),
                dh.getTrangThai(),
                dh.getNgayTao().format(formatter),
                dh.getGhiChu()
            };
            tableModel.addRow(row);
        }
    }
    
    private void loadSelectedRowToForm() {
        if (selectedRow >= 0) {
            // Tạm thời để trống - cần implement chi tiết
            txtSoLuong.setText("1");
            txtGhiChu.setText(tableModel.getValueAt(selectedRow, 5).toString());
        }
    }
    
    private void clearForm() {
        cboKhachHang.setSelectedIndex(0);
        cboDichVu.setSelectedIndex(0);
        txtSoLuong.setText("");
        txtGhiChu.setText("");
        selectedRow = -1;
        tblDonHang.clearSelection();
    }
    
    private void themDonHang() {
        if (!validateForm()) {
            return;
        }
        
        // Lấy thông tin từ form
        String khachHangStr = cboKhachHang.getSelectedItem().toString();
        String dichVuStr = cboDichVu.getSelectedItem().toString();
        
        if (khachHangStr.equals("-- Chọn khách hàng --") || dichVuStr.equals("-- Chọn dịch vụ --")) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn khách hàng và dịch vụ!", "Lỗi", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        int maKH = Integer.parseInt(khachHangStr.split(" - ")[0]);
        int maDV = Integer.parseInt(dichVuStr.split(" - ")[0]);
        int soLuong = Integer.parseInt(txtSoLuong.getText().trim());
        String ghiChu = txtGhiChu.getText().trim();
        
        // Tính tổng tiền
        DichVu dichVu = dichVuDAO.layDichVuTheoMa(maDV);
        BigDecimal tongTien = dichVu.getGia().multiply(new BigDecimal(soLuong));
        
        DonHang dh = new DonHang(maKH, tongTien, "Chờ xử lý", ghiChu);
        
        if (donHangDAO.themDonHang(dh)) {
            JOptionPane.showMessageDialog(this, "Thêm đơn hàng thành công!", "Thông báo", JOptionPane.INFORMATION_MESSAGE);
            loadData();
            clearForm();
        } else {
            JOptionPane.showMessageDialog(this, "Thêm đơn hàng thất bại!", "Lỗi", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    private void suaDonHang() {
        JOptionPane.showMessageDialog(this, "Chức năng sửa đơn hàng đang được phát triển!", "Thông báo", JOptionPane.INFORMATION_MESSAGE);
    }
    
    private void xoaDonHang() {
        if (selectedRow < 0) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn đơn hàng cần xóa!", "Thông báo", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        int maDH = (Integer) tableModel.getValueAt(selectedRow, 0);
        String khachHang = tableModel.getValueAt(selectedRow, 1).toString();
        
        int confirm = JOptionPane.showConfirmDialog(this, 
            "Bạn có chắc chắn muốn xóa đơn hàng của: " + khachHang + "?", 
            "Xác nhận xóa", 
            JOptionPane.YES_NO_OPTION);
        
        if (confirm == JOptionPane.YES_OPTION) {
            if (donHangDAO.xoaDonHang(maDH)) {
                JOptionPane.showMessageDialog(this, "Xóa đơn hàng thành công!", "Thông báo", JOptionPane.INFORMATION_MESSAGE);
                loadData();
                clearForm();
            } else {
                JOptionPane.showMessageDialog(this, "Xóa đơn hàng thất bại!", "Lỗi", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    private boolean validateForm() {
        String soLuongStr = txtSoLuong.getText().trim();
        
        if (soLuongStr.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Vui lòng nhập số lượng!", "Lỗi", JOptionPane.ERROR_MESSAGE);
            txtSoLuong.requestFocus();
            return false;
        }
        
        try {
            int soLuong = Integer.parseInt(soLuongStr);
            if (soLuong <= 0) {
                JOptionPane.showMessageDialog(this, "Số lượng phải lớn hơn 0!", "Lỗi", JOptionPane.ERROR_MESSAGE);
                txtSoLuong.requestFocus();
                return false;
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "Số lượng không hợp lệ!", "Lỗi", JOptionPane.ERROR_MESSAGE);
            txtSoLuong.requestFocus();
            return false;
        }
        
        return true;
    }
}
