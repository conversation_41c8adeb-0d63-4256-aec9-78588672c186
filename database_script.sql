-- Script tạo database và các bảng cho ứng dụng Quản lý tiệm giặt ủi
-- Tạo database
CREATE DATABASE QuanLyGiatUi;
GO

USE QuanLyGiatUi;
GO

-- Bảng Khách hàng
CREATE TABLE KhachHang (
    MaKH INT IDENTITY(1,1) PRIMARY KEY,
    HoTen NVARCHAR(100) NOT NULL,
    SoDienThoai VARCHAR(15) NOT NULL,
    DiaChi NVARCHAR(200),
    <PERSON><PERSON><PERSON>ao DATETIME DEFAULT GETDATE()
);

-- Bảng Dịch vụ
CREATE TABLE DichVu (
    MaDV INT IDENTITY(1,1) PRIMARY KEY,
    TenDichVu NVARCHAR(100) NOT NULL,
    GiaTien DECIMAL(10,2) NOT NULL,
    DonVi NVARCHAR(20) DEFAULT N'kg',
    MoTa NVARCHAR(500)
);

-- Bảng Đơn hàng
CREATE TABLE DonHang (
    MaDH INT IDENTITY(1,1) PRIMARY KEY,
    MaKH INT NOT NULL,
    NgayTao DATETIME DEFAULT GETDATE(),
    <PERSON>ayGiao DATETIME,
    TongTien DECIMAL(12,2) DEFAULT 0,
    TrangThai NVARCHAR(50) DEFAULT N'Chờ xử lý',
    GhiChu NVARCHAR(500),
    FOREIGN KEY (MaKH) REFERENCES KhachHang(MaKH)
);

-- Bảng Chi tiết đơn hàng
CREATE TABLE ChiTietDonHang (
    MaCTDH INT IDENTITY(1,1) PRIMARY KEY,
    MaDH INT NOT NULL,
    MaDV INT NOT NULL,
    SoLuong DECIMAL(8,2) NOT NULL,
    DonGia DECIMAL(10,2) NOT NULL,
    ThanhTien DECIMAL(12,2) NOT NULL,
    FOREIGN KEY (MaDH) REFERENCES DonHang(MaDH),
    FOREIGN KEY (MaDV) REFERENCES DichVu(MaDV)
);

-- Bảng Hóa đơn
CREATE TABLE HoaDon (
    MaHD INT IDENTITY(1,1) PRIMARY KEY,
    MaDH INT NOT NULL,
    NgayThanhToan DATETIME DEFAULT GETDATE(),
    TongTien DECIMAL(12,2) NOT NULL,
    PhuongThucThanhToan NVARCHAR(50) DEFAULT N'Tiền mặt',
    TrangThai NVARCHAR(50) DEFAULT N'Đã thanh toán',
    FOREIGN KEY (MaDH) REFERENCES DonHang(MaDH)
);

-- Thêm dữ liệu mẫu
-- Dữ liệu mẫu cho bảng DichVu
INSERT INTO DichVu (TenDichVu, GiaTien, DonVi, MoTa) VALUES
(N'Giặt thường', 15000, N'kg', N'Giặt quần áo thường ngày'),
(N'Giặt khô', 25000, N'kg', N'Giặt khô cho quần áo cao cấp'),
(N'Ủi quần áo', 5000, N'bộ', N'Ủi quần áo sau khi giặt'),
(N'Giặt chăn màn', 30000, N'bộ', N'Giặt chăn ga gối đệm'),
(N'Giặt vest/áo dài', 50000, N'bộ', N'Giặt vest, áo dài cao cấp');

-- Dữ liệu mẫu cho bảng KhachHang
INSERT INTO KhachHang (HoTen, SoDienThoai, DiaChi) VALUES
(N'Nguyễn Văn An', '0901234567', N'123 Đường ABC, Quận 1, TP.HCM'),
(N'Trần Thị Bình', '0912345678', N'456 Đường DEF, Quận 2, TP.HCM'),
(N'Lê Văn Cường', '0923456789', N'789 Đường GHI, Quận 3, TP.HCM'),
(N'Phạm Thị Dung', '0934567890', N'321 Đường JKL, Quận 4, TP.HCM'),
(N'Hoàng Văn Em', '0945678901', N'654 Đường MNO, Quận 5, TP.HCM');

-- Dữ liệu mẫu cho bảng DonHang
INSERT INTO DonHang (MaKH, NgayGiao, TongTien, TrangThai, GhiChu) VALUES
(1, DATEADD(day, 2, GETDATE()), 45000, N'Đang xử lý', N'Giao hàng tận nơi'),
(2, DATEADD(day, 1, GETDATE()), 75000, N'Hoàn thành', N'Khách hàng VIP'),
(3, DATEADD(day, 3, GETDATE()), 30000, N'Chờ xử lý', N'Gọi trước khi giao'),
(4, DATEADD(day, 2, GETDATE()), 100000, N'Đang xử lý', N'Giặt cẩn thận'),
(5, DATEADD(day, 1, GETDATE()), 25000, N'Hoàn thành', N'Khách hàng thường xuyên');

-- Dữ liệu mẫu cho bảng ChiTietDonHang
INSERT INTO ChiTietDonHang (MaDH, MaDV, SoLuong, DonGia, ThanhTien) VALUES
(1, 1, 2.0, 15000, 30000),
(1, 3, 3, 5000, 15000),
(2, 2, 2.5, 25000, 62500),
(2, 3, 2, 5000, 10000),
(3, 1, 2.0, 15000, 30000),
(4, 4, 2, 30000, 60000),
(4, 5, 1, 50000, 50000),
(5, 1, 1.5, 15000, 22500);

-- Tạo các stored procedure hữu ích
-- Procedure tính tổng tiền đơn hàng
CREATE PROCEDURE sp_TinhTongTienDonHang
    @MaDH INT
AS
BEGIN
    UPDATE DonHang 
    SET TongTien = (
        SELECT SUM(ThanhTien) 
        FROM ChiTietDonHang 
        WHERE MaDH = @MaDH
    )
    WHERE MaDH = @MaDH;
END;
GO

-- Procedure thống kê doanh thu theo tháng
CREATE PROCEDURE sp_ThongKeDoanhThuTheoThang
    @Nam INT,
    @Thang INT
AS
BEGIN
    SELECT 
        MONTH(dh.NgayTao) as Thang,
        YEAR(dh.NgayTao) as Nam,
        COUNT(dh.MaDH) as SoDonHang,
        SUM(dh.TongTien) as TongDoanhThu
    FROM DonHang dh
    WHERE YEAR(dh.NgayTao) = @Nam 
        AND (@Thang = 0 OR MONTH(dh.NgayTao) = @Thang)
        AND dh.TrangThai = N'Hoàn thành'
    GROUP BY YEAR(dh.NgayTao), MONTH(dh.NgayTao)
    ORDER BY Nam, Thang;
END;
GO

-- View để xem thông tin đơn hàng chi tiết
CREATE VIEW vw_DonHangChiTiet AS
SELECT 
    dh.MaDH,
    kh.HoTen as TenKhachHang,
    kh.SoDienThoai,
    dh.NgayTao,
    dh.NgayGiao,
    dh.TongTien,
    dh.TrangThai,
    dh.GhiChu
FROM DonHang dh
INNER JOIN KhachHang kh ON dh.MaKH = kh.MaKH;
GO

-- Trigger tự động cập nhật tổng tiền khi thêm/sửa chi tiết đơn hàng
CREATE TRIGGER tr_UpdateTongTien
ON ChiTietDonHang
AFTER INSERT, UPDATE, DELETE
AS
BEGIN
    -- Cập nhật tổng tiền cho các đơn hàng bị ảnh hưởng
    UPDATE DonHang 
    SET TongTien = (
        SELECT ISNULL(SUM(ThanhTien), 0)
        FROM ChiTietDonHang 
        WHERE MaDH = DonHang.MaDH
    )
    WHERE MaDH IN (
        SELECT DISTINCT MaDH FROM inserted
        UNION
        SELECT DISTINCT MaDH FROM deleted
    );
END;
GO

PRINT 'Database QuanLyGiatUi đã được tạo thành công!';
