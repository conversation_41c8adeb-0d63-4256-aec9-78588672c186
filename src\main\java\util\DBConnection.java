package util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;


/* bài này của anh nghĩa */
public class DBConnection {
    // Thông tin kết nối
    private static final String URL = "***************************************************************************************************;";
    private static final String USER = "sa";       // user SQL Server
    private static final String PASS = "sa";       // mật khẩu của user

    // Hàm lấy kết nối
    public static Connection getConnection() {
        Connection conn = null;
        try {
            // Nạp driver
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            // Kết nối CSDL
            conn = DriverManager.getConnection(URL, USER, PASS);
            System.out.println("Kết nối database thành công!");
        } catch (ClassNotFoundException e) {
            System.err.println("❌ Không tìm thấy driver JDBC: " + e.getMessage());
            System.err.println("💡 Hướng dẫn: Chạy setup.bat để download JDBC driver");
        } catch (SQLException e) {
            System.err.println("❌ Lỗi kết nối CSDL: " + e.getMessage());
            System.err.println("💡 Kiểm tra:");
            System.err.println("   - SQL Server đang chạy?");
            System.err.println("   - Database 'QuanLyGiatUi' đã tồn tại?");
            System.err.println("   - Username/Password đúng? (sa/sa)");
            System.err.println("   - Port 1433 có mở?");
        }
        return conn;
    }

    // Hàm test kết nối
    public static void main(String[] args) {
        Connection conn = DBConnection.getConnection();
        if (conn != null) {
            System.out.println(" Kết nối SQL Server thành công!");
        } else {
            System.out.println(" Kết nối SQL Server thất bại!");
            
        }
        
    }
}
