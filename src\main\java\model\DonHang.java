/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Model class cho Đơn hàng
 * <AUTHOR>
 */
public class DonHang {
    private int maDH;
    private int maKH;
    private LocalDateTime ngayTao;
    private LocalDateTime ngayGiao;
    private BigDecimal tongTien;
    private String trangThai;
    private String ghiChu;
    
    // Thông tin khách hàng (để hiển thị)
    private String tenKhachHang;
    private String soDienThoai;
    
    // Danh sách chi tiết đơn hàng
    private List<ChiTietDonHang> chiTietDonHang;
    
    // Constructor mặc định
    public DonHang() {
        this.ngayTao = LocalDateTime.now();
        this.tongTien = BigDecimal.ZERO;
        this.trangThai = "Chờ xử lý";
        this.chiTietDonHang = new ArrayList<>();
    }
    
    // Constructor có tham số
    public DonHang(int maKH, LocalDateTime ngayGiao, String ghiChu) {
        this();
        this.maKH = maKH;
        this.ngayGiao = ngayGiao;
        this.ghiChu = ghiChu;
    }
    
    // Constructor đầy đủ
    public DonHang(int maDH, int maKH, LocalDateTime ngayTao, LocalDateTime ngayGiao, 
                   BigDecimal tongTien, String trangThai, String ghiChu) {
        this.maDH = maDH;
        this.maKH = maKH;
        this.ngayTao = ngayTao;
        this.ngayGiao = ngayGiao;
        this.tongTien = tongTien;
        this.trangThai = trangThai;
        this.ghiChu = ghiChu;
        this.chiTietDonHang = new ArrayList<>();
    }

    // Getter và Setter methods
    public int getMaDH() {
        return maDH;
    }

    public void setMaDH(int maDH) {
        this.maDH = maDH;
    }

    public int getMaKH() {
        return maKH;
    }

    public void setMaKH(int maKH) {
        this.maKH = maKH;
    }

    public LocalDateTime getNgayTao() {
        return ngayTao;
    }

    public void setNgayTao(LocalDateTime ngayTao) {
        this.ngayTao = ngayTao;
    }

    public LocalDateTime getNgayGiao() {
        return ngayGiao;
    }

    public void setNgayGiao(LocalDateTime ngayGiao) {
        this.ngayGiao = ngayGiao;
    }

    public BigDecimal getTongTien() {
        return tongTien;
    }

    public void setTongTien(BigDecimal tongTien) {
        this.tongTien = tongTien;
    }

    public String getTrangThai() {
        return trangThai;
    }

    public void setTrangThai(String trangThai) {
        this.trangThai = trangThai;
    }

    public String getGhiChu() {
        return ghiChu;
    }

    public void setGhiChu(String ghiChu) {
        this.ghiChu = ghiChu;
    }

    public String getTenKhachHang() {
        return tenKhachHang;
    }

    public void setTenKhachHang(String tenKhachHang) {
        this.tenKhachHang = tenKhachHang;
    }

    public String getSoDienThoai() {
        return soDienThoai;
    }

    public void setSoDienThoai(String soDienThoai) {
        this.soDienThoai = soDienThoai;
    }

    public List<ChiTietDonHang> getChiTietDonHang() {
        return chiTietDonHang;
    }

    public void setChiTietDonHang(List<ChiTietDonHang> chiTietDonHang) {
        this.chiTietDonHang = chiTietDonHang;
    }
    
    // Phương thức thêm chi tiết đơn hàng
    public void themChiTiet(ChiTietDonHang chiTiet) {
        if (chiTietDonHang == null) {
            chiTietDonHang = new ArrayList<>();
        }
        chiTietDonHang.add(chiTiet);
        tinhLaiTongTien();
    }
    
    // Phương thức xóa chi tiết đơn hàng
    public void xoaChiTiet(int index) {
        if (chiTietDonHang != null && index >= 0 && index < chiTietDonHang.size()) {
            chiTietDonHang.remove(index);
            tinhLaiTongTien();
        }
    }
    
    // Phương thức tính lại tổng tiền
    public void tinhLaiTongTien() {
        if (chiTietDonHang == null || chiTietDonHang.isEmpty()) {
            tongTien = BigDecimal.ZERO;
            return;
        }
        
        tongTien = chiTietDonHang.stream()
                .map(ChiTietDonHang::getThanhTien)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    // Kiểm tra đơn hàng có thể hủy không
    public boolean coTheHuy() {
        return "Chờ xử lý".equals(trangThai) || "Đang xử lý".equals(trangThai);
    }
    
    // Kiểm tra đơn hàng đã hoàn thành chưa
    public boolean daHoanThanh() {
        return "Hoàn thành".equals(trangThai);
    }
    
    // Override toString method
    @Override
    public String toString() {
        return "DonHang{" +
                "maDH=" + maDH +
                ", maKH=" + maKH +
                ", ngayTao=" + ngayTao +
                ", ngayGiao=" + ngayGiao +
                ", tongTien=" + tongTien +
                ", trangThai='" + trangThai + '\'' +
                ", ghiChu='" + ghiChu + '\'' +
                '}';
    }
    
    // Override equals method
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        DonHang donHang = (DonHang) obj;
        return maDH == donHang.maDH;
    }
    
    // Override hashCode method
    @Override
    public int hashCode() {
        return Integer.hashCode(maDH);
    }
    
    // Phương thức để hiển thị thông tin ngắn gọn
    public String getDisplayText() {
        return "DH" + String.format("%04d", maDH) + " - " + tenKhachHang + " - " + tongTien + "đ";
    }
}
