/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package model;

import util.DBConnection;
import java.sql.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Data Access Object cho Đơn hàng
 * <AUTHOR>
 */
public class DonHangDAO {
    
    // Thêm đơn hàng mới
    public int themDonHang(DonHang dh) {
        String sql = "INSERT INTO DonHang (MaKH, NgayGiao, TongTien, TrangThai, GhiChu) VALUES (?, ?, ?, ?, ?)";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            pstmt.setInt(1, dh.getMaKH());
            pstmt.setTimestamp(2, dh.getNgayGiao() != null ? Timestamp.valueOf(dh.getNgayGiao()) : null);
            pstmt.setBigDecimal(3, dh.getTongTien());
            pstmt.setString(4, dh.getTrangThai());
            pstmt.setString(5, dh.getGhiChu());
            
            int result = pstmt.executeUpdate();
            if (result > 0) {
                ResultSet rs = pstmt.getGeneratedKeys();
                if (rs.next()) {
                    return rs.getInt(1); // Trả về mã đơn hàng vừa tạo
                }
            }
            
        } catch (SQLException e) {
            System.out.println("Lỗi khi thêm đơn hàng: " + e.getMessage());
        }
        return -1;
    }
    
    // Sửa thông tin đơn hàng
    public boolean suaDonHang(DonHang dh) {
        String sql = "UPDATE DonHang SET MaKH = ?, NgayGiao = ?, TongTien = ?, TrangThai = ?, GhiChu = ? WHERE MaDH = ?";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, dh.getMaKH());
            pstmt.setTimestamp(2, dh.getNgayGiao() != null ? Timestamp.valueOf(dh.getNgayGiao()) : null);
            pstmt.setBigDecimal(3, dh.getTongTien());
            pstmt.setString(4, dh.getTrangThai());
            pstmt.setString(5, dh.getGhiChu());
            pstmt.setInt(6, dh.getMaDH());
            
            int result = pstmt.executeUpdate();
            return result > 0;
            
        } catch (SQLException e) {
            System.out.println("Lỗi khi sửa đơn hàng: " + e.getMessage());
            return false;
        }
    }
    
    // Xóa đơn hàng (và chi tiết đơn hàng)
    public boolean xoaDonHang(int maDH) {
        Connection conn = null;
        try {
            conn = DBConnection.getConnection();
            conn.setAutoCommit(false);
            
            // Xóa chi tiết đơn hàng trước
            String sqlChiTiet = "DELETE FROM ChiTietDonHang WHERE MaDH = ?";
            try (PreparedStatement pstmt1 = conn.prepareStatement(sqlChiTiet)) {
                pstmt1.setInt(1, maDH);
                pstmt1.executeUpdate();
            }
            
            // Xóa đơn hàng
            String sqlDonHang = "DELETE FROM DonHang WHERE MaDH = ?";
            try (PreparedStatement pstmt2 = conn.prepareStatement(sqlDonHang)) {
                pstmt2.setInt(1, maDH);
                int result = pstmt2.executeUpdate();
                
                if (result > 0) {
                    conn.commit();
                    return true;
                }
            }
            
            conn.rollback();
            return false;
            
        } catch (SQLException e) {
            System.out.println("Lỗi khi xóa đơn hàng: " + e.getMessage());
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    System.out.println("Lỗi rollback: " + ex.getMessage());
                }
            }
            return false;
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    conn.close();
                } catch (SQLException e) {
                    System.out.println("Lỗi đóng kết nối: " + e.getMessage());
                }
            }
        }
    }
    
    // Tìm đơn hàng theo mã
    public DonHang timDonHangTheoMa(int maDH) {
        String sql = "SELECT dh.*, kh.HoTen, kh.SoDienThoai FROM DonHang dh " +
                    "INNER JOIN KhachHang kh ON dh.MaKH = kh.MaKH WHERE dh.MaDH = ?";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, maDH);
            ResultSet rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return taoDonHangTuResultSet(rs);
            }
            
        } catch (SQLException e) {
            System.out.println("Lỗi khi tìm đơn hàng: " + e.getMessage());
        }
        return null;
    }
    
    // Lấy tất cả đơn hàng
    public List<DonHang> layTatCaDonHang() {
        List<DonHang> danhSach = new ArrayList<>();
        String sql = "SELECT dh.*, kh.HoTen, kh.SoDienThoai FROM DonHang dh " +
                    "INNER JOIN KhachHang kh ON dh.MaKH = kh.MaKH ORDER BY dh.NgayTao DESC";
        try (Connection conn = DBConnection.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                danhSach.add(taoDonHangTuResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.out.println("Lỗi khi lấy danh sách đơn hàng: " + e.getMessage());
        }
        return danhSach;
    }
    
    // Lấy đơn hàng theo khách hàng
    public List<DonHang> layDonHangTheoKhachHang(int maKH) {
        List<DonHang> danhSach = new ArrayList<>();
        String sql = "SELECT dh.*, kh.HoTen, kh.SoDienThoai FROM DonHang dh " +
                    "INNER JOIN KhachHang kh ON dh.MaKH = kh.MaKH WHERE dh.MaKH = ? ORDER BY dh.NgayTao DESC";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, maKH);
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                danhSach.add(taoDonHangTuResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.out.println("Lỗi khi lấy đơn hàng theo khách hàng: " + e.getMessage());
        }
        return danhSach;
    }
    
    // Lấy đơn hàng theo trạng thái
    public List<DonHang> layDonHangTheoTrangThai(String trangThai) {
        List<DonHang> danhSach = new ArrayList<>();
        String sql = "SELECT dh.*, kh.HoTen, kh.SoDienThoai FROM DonHang dh " +
                    "INNER JOIN KhachHang kh ON dh.MaKH = kh.MaKH WHERE dh.TrangThai = ? ORDER BY dh.NgayTao DESC";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, trangThai);
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                danhSach.add(taoDonHangTuResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.out.println("Lỗi khi lấy đơn hàng theo trạng thái: " + e.getMessage());
        }
        return danhSach;
    }
    
    // Cập nhật trạng thái đơn hàng
    public boolean capNhatTrangThai(int maDH, String trangThai) {
        String sql = "UPDATE DonHang SET TrangThai = ? WHERE MaDH = ?";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, trangThai);
            pstmt.setInt(2, maDH);
            
            int result = pstmt.executeUpdate();
            return result > 0;
            
        } catch (SQLException e) {
            System.out.println("Lỗi khi cập nhật trạng thái: " + e.getMessage());
            return false;
        }
    }
    
    // Phương thức helper để tạo đối tượng DonHang từ ResultSet
    private DonHang taoDonHangTuResultSet(ResultSet rs) throws SQLException {
        int maDH = rs.getInt("MaDH");
        int maKH = rs.getInt("MaKH");
        Timestamp ngayTaoTimestamp = rs.getTimestamp("NgayTao");
        Timestamp ngayGiaoTimestamp = rs.getTimestamp("NgayGiao");
        BigDecimal tongTien = rs.getBigDecimal("TongTien");
        String trangThai = rs.getString("TrangThai");
        String ghiChu = rs.getString("GhiChu");
        String tenKhachHang = rs.getString("HoTen");
        String soDienThoai = rs.getString("SoDienThoai");
        
        LocalDateTime ngayTao = ngayTaoTimestamp != null ? ngayTaoTimestamp.toLocalDateTime() : LocalDateTime.now();
        LocalDateTime ngayGiao = ngayGiaoTimestamp != null ? ngayGiaoTimestamp.toLocalDateTime() : null;
        
        DonHang donHang = new DonHang(maDH, maKH, ngayTao, ngayGiao, tongTien, trangThai, ghiChu);
        donHang.setTenKhachHang(tenKhachHang);
        donHang.setSoDienThoai(soDienThoai);
        
        return donHang;
    }

    // Lấy chi tiết đơn hàng
    public List<ChiTietDonHang> layChiTietDonHang(int maDH) {
        List<ChiTietDonHang> danhSach = new ArrayList<>();
        String sql = "SELECT ct.*, dv.TenDichVu, dv.DonVi FROM ChiTietDonHang ct " +
                    "INNER JOIN DichVu dv ON ct.MaDV = dv.MaDV WHERE ct.MaDH = ?";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, maDH);
            ResultSet rs = pstmt.executeQuery();

            while (rs.next()) {
                ChiTietDonHang chiTiet = new ChiTietDonHang();
                chiTiet.setMaCTDH(rs.getInt("MaCTDH"));
                chiTiet.setMaDH(rs.getInt("MaDH"));
                chiTiet.setMaDV(rs.getInt("MaDV"));
                chiTiet.setSoLuong(rs.getBigDecimal("SoLuong"));
                chiTiet.setDonGia(rs.getBigDecimal("DonGia"));
                chiTiet.setThanhTien(rs.getBigDecimal("ThanhTien"));
                chiTiet.setTenDichVu(rs.getString("TenDichVu"));
                chiTiet.setDonVi(rs.getString("DonVi"));

                danhSach.add(chiTiet);
            }

        } catch (SQLException e) {
            System.out.println("Lỗi khi lấy chi tiết đơn hàng: " + e.getMessage());
        }
        return danhSach;
    }

    // Thêm chi tiết đơn hàng
    public boolean themChiTietDonHang(ChiTietDonHang chiTiet) {
        String sql = "INSERT INTO ChiTietDonHang (MaDH, MaDV, SoLuong, DonGia, ThanhTien) VALUES (?, ?, ?, ?, ?)";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, chiTiet.getMaDH());
            pstmt.setInt(2, chiTiet.getMaDV());
            pstmt.setBigDecimal(3, chiTiet.getSoLuong());
            pstmt.setBigDecimal(4, chiTiet.getDonGia());
            pstmt.setBigDecimal(5, chiTiet.getThanhTien());

            int result = pstmt.executeUpdate();
            return result > 0;

        } catch (SQLException e) {
            System.out.println("Lỗi khi thêm chi tiết đơn hàng: " + e.getMessage());
            return false;
        }
    }

    // Thống kê doanh thu theo tháng
    public BigDecimal thongKeDoanhThuTheoThang(int nam, int thang) {
        String sql = "SELECT SUM(TongTien) FROM DonHang WHERE YEAR(NgayTao) = ? AND MONTH(NgayTao) = ? AND TrangThai = N'Hoàn thành'";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, nam);
            pstmt.setInt(2, thang);
            ResultSet rs = pstmt.executeQuery();

            if (rs.next()) {
                BigDecimal doanhThu = rs.getBigDecimal(1);
                return doanhThu != null ? doanhThu : BigDecimal.ZERO;
            }

        } catch (SQLException e) {
            System.out.println("Lỗi khi thống kê doanh thu: " + e.getMessage());
        }
        return BigDecimal.ZERO;
    }

    // Đếm số đơn hàng theo trạng thái
    public int demDonHangTheoTrangThai(String trangThai) {
        String sql = "SELECT COUNT(*) FROM DonHang WHERE TrangThai = ?";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, trangThai);
            ResultSet rs = pstmt.executeQuery();

            if (rs.next()) {
                return rs.getInt(1);
            }

        } catch (SQLException e) {
            System.out.println("Lỗi khi đếm đơn hàng: " + e.getMessage());
        }
        return 0;
    }
}
