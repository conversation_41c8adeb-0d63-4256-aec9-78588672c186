@echo off
echo ========================================
echo    KIEM TRA MOI TRUONG HE THONG
echo ========================================
echo.

set ERROR_COUNT=0

REM Kiem tra Java
echo [1/4] Kiem tra Java...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java chua duoc cai dat hoac chua duoc them vao PATH
    echo 💡 Download tai: https://www.oracle.com/java/technologies/downloads/
    set /a ERROR_COUNT+=1
) else (
    echo ✅ Java da duoc cai dat
    java -version 2>&1 | findstr "version"
)
echo.

REM Kiem tra javac
echo [2/4] Kiem tra Java Compiler...
javac -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java Compiler (javac) khong tim thay
    echo 💡 Can cai dat JDK (khong phai JRE)
    set /a ERROR_COUNT+=1
) else (
    echo ✅ Java Compiler da san sang
    javac -version
)
echo.

REM Kiem tra SQL Server
echo [3/4] Kiem tra SQL Server...
sc query MSSQLSERVER >nul 2>&1
if %errorlevel% neq 0 (
    sc query "MSSQL$SQLEXPRESS" >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ SQL Server khong duoc cai dat hoac khong chay
        echo 💡 Cai dat SQL Server Express hoac Developer Edition
        set /a ERROR_COUNT+=1
    ) else (
        echo ✅ SQL Server Express dang chay
    )
) else (
    echo ✅ SQL Server dang chay
)
echo.

REM Kiem tra JDBC Driver
echo [4/4] Kiem tra JDBC Driver...
if exist "lib\mssql-jdbc-12.6.1.jre11.jar" (
    echo ✅ JDBC Driver da co trong thu muc lib/
) else (
    if exist "target\dependency\mssql-jdbc-12.6.1.jre11.jar" (
        echo ✅ JDBC Driver da co trong thu muc target/dependency/
    ) else (
        echo ❌ JDBC Driver chua duoc download
        echo 💡 Chay setup.bat de download tu dong
        set /a ERROR_COUNT+=1
    )
)
echo.

REM Kiem tra thu muc
echo [BONUS] Kiem tra cau truc thu muc...
if exist "src\main\java" (
    echo ✅ Thu muc source code: OK
) else (
    echo ❌ Thu muc source code khong tim thay
    set /a ERROR_COUNT+=1
)

if exist "database_script.sql" (
    echo ✅ Database script: OK
) else (
    echo ❌ Database script khong tim thay
    set /a ERROR_COUNT+=1
)

echo.
echo ========================================
if %ERROR_COUNT% equ 0 (
    echo ✅ TAT CA DIEU KIEN DA SAN SANG!
    echo Ban co the chay ung dung bang: run.bat
) else (
    echo ❌ CO %ERROR_COUNT% VAN DE CAN KHAC PHUC
    echo Vui long sua cac van de tren truoc khi chay ung dung
)
echo ========================================
echo.
pause
