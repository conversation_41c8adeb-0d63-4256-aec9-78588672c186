import view.MainFrame;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;

/**
 * Class chính để khởi chạy ứng dụng
 * <AUTHOR>
 */
public class Main {
    public static void main(String[] args) {
        try {
            // Thiết lập Look and Feel
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
            
            // Khởi chạy ứng dụng trên EDT
            SwingUtilities.invokeLater(() -> {
                try {
                    MainFrame frame = new MainFrame();
                    frame.setVisible(true);
                    System.out.println("✅ Ứng dụng đã khởi chạy thành công!");
                } catch (Exception e) {
                    System.err.println("❌ Lỗi khởi chạy ứng dụng: " + e.getMessage());
                    e.printStackTrace();
                }
            });
            
        } catch (Exception e) {
            System.err.println("❌ Lỗi thiết lập Look and Feel: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
