@echo off
echo ========================================
echo    Ung dung Quan ly Tiem Giat Ui
echo ========================================
echo.

REM Kiem tra Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Java chua duoc cai dat hoac chua duoc them vao PATH
    echo Vui long cai dat Java JDK 21 hoac cao hon
    echo Download tai: https://www.oracle.com/java/technologies/downloads/
    pause
    exit /b 1
)

echo [INFO] Java da duoc cai dat
echo.

REM Kiem tra Maven
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] Maven chua duoc cai dat
    echo Dang thu compile bang javac...
    echo.
    
    REM Tao thu muc target/classes neu chua co
    if not exist "target\classes" mkdir "target\classes"
    
    REM Compile bang javac
    echo [INFO] Dang compile...
    javac -cp "target/dependency/*" -d "target/classes" -sourcepath "src/main/java" src/main/java/com/mycompany/giatui/app/GiatUiAPP.java src/main/java/model/*.java src/main/java/util/*.java src/main/java/view/*.java
    
    if %errorlevel% neq 0 (
        echo [ERROR] Compile that bai
        pause
        exit /b 1
    )
    
    echo [INFO] Compile thanh cong
    echo [INFO] Dang chay ung dung...
    echo.
    
    REM Chay ung dung
    java -cp "target/classes;target/dependency/*" com.mycompany.giatui.app.GiatUiAPP
    
) else (
    echo [INFO] Maven da duoc cai dat
    echo [INFO] Dang compile va chay ung dung...
    echo.
    
    REM Su dung Maven
    mvn clean compile exec:java
)

echo.
echo [INFO] Ung dung da ket thuc
pause
