/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package model;

import util.DBConnection;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Data Access Object cho Khách hàng
 * <AUTHOR>
 */
public class KhachHangDAO {

    // Thêm khách hàng mới
    public boolean themKhachHang(KhachHang kh) {
        String sql = "INSERT INTO KhachHang (HoTen, SoDienThoai, DiaChi) VALUES (?, ?, ?)";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, kh.getHoTen());
            pstmt.setString(2, kh.getSoDienThoai());
            pstmt.setString(3, kh.getDiaChi());

            int result = pstmt.executeUpdate();
            return result > 0;

        } catch (SQLException e) {
            System.out.println("Lỗi khi thêm khách hàng: " + e.getMessage());
            return false;
        }
    }

    // Sửa thông tin khách hàng
    public boolean suaKhachHang(KhachHang kh) {
        String sql = "UPDATE KhachHang SET HoTen = ?, SoDienThoai = ?, DiaChi = ? WHERE MaKH = ?";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, kh.getHoTen());
            pstmt.setString(2, kh.getSoDienThoai());
            pstmt.setString(3, kh.getDiaChi());
            pstmt.setInt(4, kh.getMaKH());

            int result = pstmt.executeUpdate();
            return result > 0;

        } catch (SQLException e) {
            System.out.println("Lỗi khi sửa khách hàng: " + e.getMessage());
            return false;
        }
    }

    // Xóa khách hàng
    public boolean xoaKhachHang(int maKH) {
        String sql = "DELETE FROM KhachHang WHERE MaKH = ?";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, maKH);

            int result = pstmt.executeUpdate();
            return result > 0;

        } catch (SQLException e) {
            System.out.println("Lỗi khi xóa khách hàng: " + e.getMessage());
            return false;
        }
    }

    // Tìm khách hàng theo mã
    public KhachHang timKhachHangTheoMa(int maKH) {
        String sql = "SELECT * FROM KhachHang WHERE MaKH = ?";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, maKH);
            ResultSet rs = pstmt.executeQuery();

            if (rs.next()) {
                return taoKhachHangTuResultSet(rs);
            }

        } catch (SQLException e) {
            System.out.println("Lỗi khi tìm khách hàng: " + e.getMessage());
        }
        return null;
    }

    // Tìm khách hàng theo số điện thoại
    public KhachHang timKhachHangTheoSDT(String sdt) {
        String sql = "SELECT * FROM KhachHang WHERE SoDienThoai = ?";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, sdt);
            ResultSet rs = pstmt.executeQuery();

            if (rs.next()) {
                return taoKhachHangTuResultSet(rs);
            }

        } catch (SQLException e) {
            System.out.println("Lỗi khi tìm khách hàng theo SDT: " + e.getMessage());
        }
        return null;
    }

    // Tìm kiếm khách hàng theo tên (tìm kiếm gần đúng)
    public List<KhachHang> timKhachHangTheoTen(String ten) {
        List<KhachHang> danhSach = new ArrayList<>();
        String sql = "SELECT * FROM KhachHang WHERE HoTen LIKE ? ORDER BY HoTen";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, "%" + ten + "%");
            ResultSet rs = pstmt.executeQuery();

            while (rs.next()) {
                danhSach.add(taoKhachHangTuResultSet(rs));
            }

        } catch (SQLException e) {
            System.out.println("Lỗi khi tìm khách hàng theo tên: " + e.getMessage());
        }
        return danhSach;
    }

    // Lấy tất cả khách hàng
    public List<KhachHang> layTatCaKhachHang() {
        List<KhachHang> danhSach = new ArrayList<>();
        String sql = "SELECT * FROM KhachHang ORDER BY NgayTao DESC";
        try (Connection conn = DBConnection.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                danhSach.add(taoKhachHangTuResultSet(rs));
            }

        } catch (SQLException e) {
            System.out.println("Lỗi khi lấy danh sách khách hàng: " + e.getMessage());
        }
        return danhSach;
    }

    // Đếm tổng số khách hàng
    public int demTongSoKhachHang() {
        String sql = "SELECT COUNT(*) FROM KhachHang";
        try (Connection conn = DBConnection.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            if (rs.next()) {
                return rs.getInt(1);
            }

        } catch (SQLException e) {
            System.out.println("Lỗi khi đếm khách hàng: " + e.getMessage());
        }
        return 0;
    }

    // Kiểm tra số điện thoại đã tồn tại chưa
    public boolean kiemTraSDTTonTai(String sdt, int maKHLoaiTru) {
        String sql = "SELECT COUNT(*) FROM KhachHang WHERE SoDienThoai = ? AND MaKH != ?";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, sdt);
            pstmt.setInt(2, maKHLoaiTru);
            ResultSet rs = pstmt.executeQuery();

            if (rs.next()) {
                return rs.getInt(1) > 0;
            }

        } catch (SQLException e) {
            System.out.println("Lỗi khi kiểm tra SDT: " + e.getMessage());
        }
        return false;
    }

    // Phương thức helper để tạo đối tượng KhachHang từ ResultSet
    private KhachHang taoKhachHangTuResultSet(ResultSet rs) throws SQLException {
        int maKH = rs.getInt("MaKH");
        String hoTen = rs.getString("HoTen");
        String soDienThoai = rs.getString("SoDienThoai");
        String diaChi = rs.getString("DiaChi");
        Timestamp ngayTaoTimestamp = rs.getTimestamp("NgayTao");
        LocalDateTime ngayTao = ngayTaoTimestamp != null ? ngayTaoTimestamp.toLocalDateTime() : LocalDateTime.now();

        return new KhachHang(maKH, hoTen, soDienThoai, diaChi, ngayTao);
    }

    // Lấy khách hàng theo phân trang
    public List<KhachHang> layKhachHangTheoPhanTrang(int trang, int soPhanTuMoiTrang) {
        List<KhachHang> danhSach = new ArrayList<>();
        String sql = "SELECT * FROM KhachHang ORDER BY NgayTao DESC OFFSET ? ROWS FETCH NEXT ? ROWS ONLY";
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            int offset = (trang - 1) * soPhanTuMoiTrang;
            pstmt.setInt(1, offset);
            pstmt.setInt(2, soPhanTuMoiTrang);

            ResultSet rs = pstmt.executeQuery();
            while (rs.next()) {
                danhSach.add(taoKhachHangTuResultSet(rs));
            }

        } catch (SQLException e) {
            System.out.println("Lỗi khi lấy khách hàng theo phân trang: " + e.getMessage());
        }
        return danhSach;
    }
}
