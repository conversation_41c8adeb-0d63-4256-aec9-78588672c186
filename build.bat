@echo off
echo ========================================
echo    BUILD SCRIPT - QUAN LY TIEM GIAT LA
echo ========================================

echo.
echo 1. Tao thu muc build...
if not exist "build" mkdir build
if not exist "build\classes" mkdir build\classes

echo.
echo 2. Compile Java files...
javac -d build\classes -cp "lib\*" src\main\java\*.java src\main\java\model\*.java src\main\java\view\*.java src\main\java\util\*.java

if %ERRORLEVEL% EQU 0 (
    echo ✅ Compile thanh cong!
    echo.
    echo 3. Chay ung dung...
    java -cp "build\classes;lib\*" Main
) else (
    echo ❌ Compile that bai!
    echo Kiem tra lai cac loi trong code.
)

echo.
echo Build script hoan thanh.
pause
