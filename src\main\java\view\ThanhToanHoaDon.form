<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JPanelFormInfo">
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <Component id="jPanel1" max="32767" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="459" max="-2" attributes="0"/>
          </Group>
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace min="-2" pref="32" max="-2" attributes="0"/>
              <Component id="jPanel2" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="32767" attributes="0"/>
              <Component id="jLabel8" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="95" max="-2" attributes="0"/>
              <Component id="cbodichvu" min="-2" pref="217" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="100" max="-2" attributes="0"/>
          </Group>
          <Group type="102" attributes="0">
              <EmptySpace min="-2" pref="105" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="jScrollPane1" min="-2" pref="801" max="-2" attributes="0"/>
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace min="-2" pref="9" max="-2" attributes="0"/>
                      <Component id="jLabel2" min="-2" pref="173" max="-2" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace max="32767" attributes="0"/>
          </Group>
          <Group type="103" rootIndex="1" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="200" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Group type="102" alignment="0" attributes="0">
                          <EmptySpace min="-2" pref="8" max="-2" attributes="0"/>
                          <Component id="btntinhtien" min="-2" pref="116" max="-2" attributes="0"/>
                          <EmptySpace min="-2" pref="114" max="-2" attributes="0"/>
                          <Component id="btnthanhtoan" min="-2" pref="116" max="-2" attributes="0"/>
                          <EmptySpace pref="123" max="32767" attributes="0"/>
                          <Component id="btninhoadon" min="-2" pref="116" max="-2" attributes="0"/>
                          <EmptySpace min="-2" pref="168" max="-2" attributes="0"/>
                      </Group>
                      <Group type="102" alignment="0" attributes="0">
                          <Group type="103" groupAlignment="1" max="-2" attributes="0">
                              <Component id="txttongtien" alignment="0" pref="661" max="32767" attributes="0"/>
                              <Component id="txtsoluong" alignment="0" max="32767" attributes="0"/>
                              <Component id="txtdongia" alignment="0" max="32767" attributes="0"/>
                              <Component id="txtmadonhang" alignment="0" min="-2" pref="218" max="-2" attributes="0"/>
                              <Component id="txthoten" alignment="0" max="32767" attributes="0"/>
                          </Group>
                          <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
                      </Group>
                  </Group>
              </Group>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" attributes="0">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="jPanel1" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
                  </Group>
              </Group>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" attributes="0">
                      <EmptySpace min="-2" pref="79" max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="jLabel8" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="cbodichvu" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                      </Group>
                  </Group>
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace min="-2" pref="33" max="-2" attributes="0"/>
                      <Component id="jPanel2" min="-2" max="-2" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace pref="19" max="32767" attributes="0"/>
              <Component id="jLabel2" min="-2" pref="30" max="-2" attributes="0"/>
              <EmptySpace type="unrelated" max="-2" attributes="0"/>
              <Component id="jScrollPane1" min="-2" pref="210" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="18" max="-2" attributes="0"/>
          </Group>
          <Group type="103" rootIndex="1" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="78" max="-2" attributes="0"/>
                  <Component id="txthoten" min="-2" pref="30" max="-2" attributes="0"/>
                  <EmptySpace type="separate" max="-2" attributes="0"/>
                  <Component id="txtmadonhang" min="-2" pref="30" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="26" max="-2" attributes="0"/>
                  <Component id="txtdongia" min="-2" pref="30" max="-2" attributes="0"/>
                  <EmptySpace type="separate" max="-2" attributes="0"/>
                  <Component id="txtsoluong" min="-2" pref="30" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="28" max="-2" attributes="0"/>
                  <Component id="txttongtien" min="-2" pref="30" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="42" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="btntinhtien" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                      <Component id="btnthanhtoan" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                      <Component id="btninhoadon" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace pref="304" max="32767" attributes="0"/>
              </Group>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JPanel" name="jPanel1">

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <EmptySpace min="0" pref="267" max="32767" attributes="0"/>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <EmptySpace min="0" pref="44" max="32767" attributes="0"/>
          </Group>
        </DimensionLayout>
      </Layout>
    </Container>
    <Component class="javax.swing.JTextField" name="txttongtien">
    </Component>
    <Component class="javax.swing.JButton" name="btntinhtien">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="T&#xed;nh Ti&#x1ec1;n "/>
      </Properties>
    </Component>
    <Component class="javax.swing.JButton" name="btnthanhtoan">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Thanh To&#xe1;n "/>
      </Properties>
    </Component>
    <Component class="javax.swing.JButton" name="btninhoadon">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="14" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="In Ho&#xe1; &#x110;&#x1a1;n"/>
      </Properties>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane1">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTable" name="tblthongtinhoadon">
          <Properties>
            <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
              <Table columnCount="5" rowCount="5">
                <Column editable="true" title="Kh&#xe1;ch H&#xe0;ng " type="java.lang.Object"/>
                <Column editable="true" title="M&#xe3; &#x110;&#x1a1;n" type="java.lang.Object"/>
                <Column editable="true" title="&#x110;&#x1a1;n Gi&#xe1;" type="java.lang.Object"/>
                <Column editable="true" title="S&#x1ed1; L&#x1b0;&#x1ee3;ng " type="java.lang.Object"/>
                <Column editable="true" title="T&#x1ed5;ng Ti&#x1ec1;n" type="java.lang.Object"/>
              </Table>
            </Property>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JTextField" name="txthoten">
    </Component>
    <Component class="javax.swing.JTextField" name="txtmadonhang">
    </Component>
    <Component class="javax.swing.JTextField" name="txtdongia">
    </Component>
    <Component class="javax.swing.JTextField" name="txtsoluong">
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel1">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="24" style="0"/>
        </Property>
        <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="ff" green="0" red="0" type="rgb"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Thanh To&#xe1;n Ho&#xe1; &#x110;&#x1a1;n"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel2">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="16" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Th&#xf4;ng Tin H&#xf3;a &#x110;&#x1a1;n"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel8">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="16" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="D&#x1ecb;ch  V&#x1ee5;"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JComboBox" name="cbodichvu">
      <Properties>
        <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
          <StringArray count="4">
            <StringItem index="0" value="Item 1"/>
            <StringItem index="1" value="Item 2"/>
            <StringItem index="2" value="Item 3"/>
            <StringItem index="3" value="Item 4"/>
          </StringArray>
        </Property>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_TypeParameters" type="java.lang.String" value="&lt;String&gt;"/>
      </AuxValues>
    </Component>
    <Container class="javax.swing.JPanel" name="jPanel2">

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="1" attributes="0">
                  <EmptySpace pref="56" max="32767" attributes="0"/>
                  <Group type="103" groupAlignment="1" attributes="0">
                      <Component id="jLabel7" min="-2" max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Component id="jLabel5" min="-2" max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="1" attributes="0">
                              <Component id="jLabel4" min="-2" max="-2" attributes="0"/>
                              <Component id="jLabel3" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <Component id="jLabel6" min="-2" max="-2" attributes="0"/>
                      </Group>
                  </Group>
                  <EmptySpace min="-2" pref="28" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="1" attributes="0">
                  <Component id="jLabel3" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="29" max="-2" attributes="0"/>
                  <Component id="jLabel4" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="38" max="-2" attributes="0"/>
                  <Component id="jLabel5" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="26" max="-2" attributes="0"/>
                  <Component id="jLabel6" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="34" max="-2" attributes="0"/>
                  <Component id="jLabel7" min="-2" max="-2" attributes="0"/>
                  <EmptySpace pref="91" max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JLabel" name="jLabel4">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="M&#xe3; &#x110;&#x1a1;n"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel5">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="&#x110;&#x1a1;n Gi&#xe1;"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel6">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="S&#x1ed1; L&#x1b0;&#x1ee3;ng"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel7">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="T&#x1ed5;ng Ti&#x1ec1;n"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel3">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="H&#x1ecd; T&#xea;n "/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
  </SubComponents>
</Form>
